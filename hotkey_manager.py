import keyboard
import threading
from typing import Callable, Optional
import logging

class HotkeyManager:
    """Manages global hotkey detection for the work timer."""
    
    def __init__(self):
        self.hotkey_combination = "ctrl+shift+t"
        self.callback: Optional[Callable] = None
        self._listener_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
        self._is_running = False
        
        # Set up logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def set_hotkey(self, hotkey_combination: str):
        """Set the hotkey combination (e.g., 'ctrl+shift+t')."""
        was_running = self._is_running
        if was_running:
            self.stop_listening()
        
        self.hotkey_combination = hotkey_combination
        
        if was_running:
            self.start_listening()
    
    def set_callback(self, callback: Callable):
        """Set the callback function to call when hotkey is pressed."""
        self.callback = callback
    
    def start_listening(self):
        """Start listening for the hotkey in a background thread."""
        if self._is_running:
            self.logger.warning("Hotkey listener is already running")
            return
        
        if not self.callback:
            self.logger.error("No callback set for hotkey")
            return
        
        self._stop_event.clear()
        self._listener_thread = threading.Thread(target=self._listen_loop, daemon=True)
        self._listener_thread.start()
        self._is_running = True
        self.logger.info(f"Started listening for hotkey: {self.hotkey_combination}")
    
    def stop_listening(self):
        """Stop listening for the hotkey."""
        if not self._is_running:
            return
        
        self._stop_event.set()
        self._is_running = False
        
        # Unhook all keyboard events
        try:
            keyboard.unhook_all()
        except Exception as e:
            self.logger.error(f"Error unhooking keyboard events: {e}")
        
        if self._listener_thread and self._listener_thread.is_alive():
            self._listener_thread.join(timeout=1.0)
        
        self.logger.info("Stopped listening for hotkey")
    
    def _listen_loop(self):
        """Main listening loop that runs in a background thread."""
        try:
            # Register the hotkey
            keyboard.add_hotkey(self.hotkey_combination, self._on_hotkey_pressed)
            
            # Keep the thread alive until stop is requested
            while not self._stop_event.is_set():
                self._stop_event.wait(0.1)
                
        except Exception as e:
            self.logger.error(f"Error in hotkey listener: {e}")
        finally:
            try:
                keyboard.unhook_all()
            except:
                pass
    
    def _on_hotkey_pressed(self):
        """Called when the hotkey is pressed."""
        try:
            if self.callback:
                self.logger.info(f"Hotkey {self.hotkey_combination} pressed")
                self.callback()
        except Exception as e:
            self.logger.error(f"Error in hotkey callback: {e}")
    
    def is_listening(self) -> bool:
        """Check if the hotkey listener is currently active."""
        return self._is_running
    
    def __del__(self):
        """Cleanup when the object is destroyed."""
        self.stop_listening()

# Test function for the hotkey manager
def test_hotkey_manager():
    """Test function to verify hotkey functionality."""
    def test_callback():
        print("Hotkey pressed!")
    
    manager = HotkeyManager()
    manager.set_callback(test_callback)
    manager.start_listening()
    
    print(f"Listening for hotkey: {manager.hotkey_combination}")
    print("Press the hotkey to test, or Ctrl+C to exit")
    
    try:
        import time
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nStopping...")
        manager.stop_listening()

if __name__ == "__main__":
    test_hotkey_manager()
