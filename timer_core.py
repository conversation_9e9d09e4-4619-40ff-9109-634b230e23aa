import sqlite3
import datetime
import threading
from typing import Optional, List, Tuple

class WorkSession:
    """Represents a single work session with start and end times."""
    
    def __init__(self, start_time: datetime.datetime, end_time: Optional[datetime.datetime] = None, session_id: Optional[int] = None):
        self.session_id = session_id
        self.start_time = start_time
        self.end_time = end_time
    
    @property
    def duration(self) -> datetime.timedelta:
        """Calculate the duration of the session."""
        if self.end_time is None:
            return datetime.datetime.now() - self.start_time
        return self.end_time - self.start_time
    
    @property
    def is_active(self) -> bool:
        """Check if the session is currently active (no end time)."""
        return self.end_time is None

class WorkTimer:
    """Core timer functionality for tracking work sessions."""
    
    def __init__(self, db_path: str = "work_sessions.db"):
        self.db_path = db_path
        self.current_session: Optional[WorkSession] = None
        self._lock = threading.Lock()
        self._init_database()
        self._load_active_session()
    
    def _init_database(self):
        """Initialize the SQLite database."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS work_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    start_time TEXT NOT NULL,
                    end_time TEXT,
                    duration_seconds INTEGER
                )
            """)
            conn.commit()
    
    def _load_active_session(self):
        """Load any active session from the database on startup."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT id, start_time FROM work_sessions 
                WHERE end_time IS NULL 
                ORDER BY start_time DESC 
                LIMIT 1
            """)
            row = cursor.fetchone()
            if row:
                session_id, start_time_str = row
                start_time = datetime.datetime.fromisoformat(start_time_str)
                self.current_session = WorkSession(start_time, session_id=session_id)
    
    def start_timer(self) -> bool:
        """Start a new work session. Returns True if started, False if already running."""
        with self._lock:
            if self.current_session and self.current_session.is_active:
                return False
            
            start_time = datetime.datetime.now()
            
            # Save to database
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    INSERT INTO work_sessions (start_time) VALUES (?)
                """, (start_time.isoformat(),))
                session_id = cursor.lastrowid
                conn.commit()
            
            self.current_session = WorkSession(start_time, session_id=session_id)
            return True
    
    def stop_timer(self) -> Optional[WorkSession]:
        """Stop the current work session. Returns the completed session or None if no active session."""
        with self._lock:
            if not self.current_session or not self.current_session.is_active:
                return None
            
            end_time = datetime.datetime.now()
            duration = end_time - self.current_session.start_time
            
            # Update database
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    UPDATE work_sessions 
                    SET end_time = ?, duration_seconds = ? 
                    WHERE id = ?
                """, (end_time.isoformat(), int(duration.total_seconds()), self.current_session.session_id))
                conn.commit()
            
            self.current_session.end_time = end_time
            completed_session = self.current_session
            self.current_session = None
            return completed_session
    
    def toggle_timer(self) -> Tuple[bool, Optional[WorkSession]]:
        """Toggle timer state. Returns (is_now_running, completed_session_if_stopped)."""
        if self.is_running():
            completed_session = self.stop_timer()
            return False, completed_session
        else:
            started = self.start_timer()
            return started, None
    
    def is_running(self) -> bool:
        """Check if timer is currently running."""
        return self.current_session is not None and self.current_session.is_active
    
    def get_current_session_duration(self) -> datetime.timedelta:
        """Get duration of current session, or zero if not running."""
        if self.current_session and self.current_session.is_active:
            return self.current_session.duration
        return datetime.timedelta(0)
    
    def get_today_total(self) -> datetime.timedelta:
        """Get total time worked today."""
        today = datetime.date.today()
        return self.get_date_total(today)
    
    def get_date_total(self, date: datetime.date) -> datetime.timedelta:
        """Get total time worked on a specific date."""
        start_of_day = datetime.datetime.combine(date, datetime.time.min)
        end_of_day = datetime.datetime.combine(date, datetime.time.max)
        
        total_seconds = 0
        
        with sqlite3.connect(self.db_path) as conn:
            # Get completed sessions for the day
            cursor = conn.execute("""
                SELECT duration_seconds FROM work_sessions 
                WHERE start_time >= ? AND start_time <= ? AND end_time IS NOT NULL
            """, (start_of_day.isoformat(), end_of_day.isoformat()))
            
            for row in cursor:
                total_seconds += row[0]
        
        # Add current session if it's today and active
        if (self.current_session and self.current_session.is_active and 
            self.current_session.start_time.date() == date):
            total_seconds += int(self.current_session.duration.total_seconds())
        
        return datetime.timedelta(seconds=total_seconds)
    
    def get_week_total(self, week_start: datetime.date = None) -> datetime.timedelta:
        """Get total time worked in a week. If week_start is None, uses current week (Monday start)."""
        if week_start is None:
            today = datetime.date.today()
            week_start = today - datetime.timedelta(days=today.weekday())
        
        week_end = week_start + datetime.timedelta(days=6)
        
        total_duration = datetime.timedelta(0)
        current_date = week_start
        
        while current_date <= week_end:
            total_duration += self.get_date_total(current_date)
            current_date += datetime.timedelta(days=1)
        
        return total_duration
    
    def get_recent_sessions(self, days: int = 7) -> List[WorkSession]:
        """Get recent completed sessions."""
        cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days)
        
        sessions = []
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT id, start_time, end_time FROM work_sessions 
                WHERE start_time >= ? AND end_time IS NOT NULL
                ORDER BY start_time DESC
            """, (cutoff_date.isoformat(),))
            
            for row in cursor:
                session_id, start_time_str, end_time_str = row
                start_time = datetime.datetime.fromisoformat(start_time_str)
                end_time = datetime.datetime.fromisoformat(end_time_str)
                sessions.append(WorkSession(start_time, end_time, session_id))
        
        return sessions
