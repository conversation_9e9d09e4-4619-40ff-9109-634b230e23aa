#!/usr/bin/env python3
"""
Work Timer Application

A comprehensive work timer with hotkey support, system tray integration,
and weekly reporting for timesheet management.

Usage:
    python work_timer.py [options]

Options:
    --gui           Show GUI window on startup
    --no-tray       Don't start system tray (GUI only mode)
    --config FILE   Use custom config file
    --help          Show this help message
"""

import sys
import argparse
import threading
import signal
import time
from typing import Optional

# Import our modules
from timer_core import WorkTimer
from hotkey_manager import HotkeyManager
from notifications import NotificationManager, TimerNotificationIntegration
from system_tray import SystemTrayManager
from gui import TimerGUI
from config import ConfigManager
from reporting import WeeklyReporter

class WorkTimerApp:
    """Main application class that coordinates all components."""
    
    def __init__(self, config_file: str = "config.ini"):
        self.config = ConfigManager(config_file)
        
        # Initialize core components
        db_path = self.config.get_database_path()
        self.timer = WorkTimer(db_path)
        self.notifications = NotificationManager(self.timer)
        self.hotkey_manager = HotkeyManager()
        self.reporter = WeeklyReporter(self.timer)
        
        # Initialize UI components
        self.gui: Optional[TimerGUI] = None
        self.tray: Optional[SystemTrayManager] = None
        
        # Integration components
        self.notification_integration: Optional[TimerNotificationIntegration] = None
        
        # Application state
        self.running = False
        self._setup_signal_handlers()
        self._configure_components()
    
    def _setup_signal_handlers(self):
        """Set up signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            print(f"\nReceived signal {signum}, shutting down...")
            self.shutdown()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def _configure_components(self):
        """Configure all components based on settings."""
        # Configure notifications
        notification_settings = self.config.get_notification_settings()
        self.notifications.set_notification_preferences(
            notify_on_start=notification_settings['notify_on_start'],
            notify_on_stop=notification_settings['notify_on_stop'],
            notify_weekly_summary=notification_settings['notify_weekly_summary'],
            weekly_day=notification_settings['weekly_notification_day'],
            weekly_time=(notification_settings['weekly_notification_hour'], 
                        notification_settings['weekly_notification_minute'])
        )
        
        # Configure hotkeys
        toggle_hotkey = self.config.get_hotkey('toggle_timer')
        if toggle_hotkey:
            self.hotkey_manager.set_hotkey(toggle_hotkey)
            self.hotkey_manager.set_callback(self._hotkey_toggle_timer)
    
    def _hotkey_toggle_timer(self):
        """Callback for hotkey timer toggle."""
        try:
            is_running, completed_session = self.timer.toggle_timer()
            
            if is_running:
                print("Timer started via hotkey")
            else:
                if completed_session:
                    duration_str = self._format_duration(completed_session.duration)
                    print(f"Timer stopped via hotkey - Session: {duration_str}")
                    
        except Exception as e:
            print(f"Error toggling timer via hotkey: {e}")
    
    def start_gui(self):
        """Start the GUI interface."""
        if self.gui is None:
            self.gui = TimerGUI(self.timer)
        
        # Run GUI in main thread
        try:
            self.gui.run()
        except Exception as e:
            print(f"GUI error: {e}")
        finally:
            self.gui = None
    
    def start_tray(self):
        """Start the system tray interface."""
        if self.tray is None:
            self.tray = SystemTrayManager(self.timer, self.notifications)
        
        try:
            self.tray.start()  # This blocks
        except Exception as e:
            print(f"System tray error: {e}")
        finally:
            self.tray = None
    
    def start_background_services(self):
        """Start background services (hotkeys, notifications)."""
        # Start hotkey listener
        try:
            self.hotkey_manager.start_listening()
            print(f"Hotkey listener started: {self.config.get_hotkey('toggle_timer')}")
        except Exception as e:
            print(f"Failed to start hotkey listener: {e}")
        
        # Start notification integration
        try:
            self.notification_integration = TimerNotificationIntegration(
                self.timer, self.notifications
            )
            self.notification_integration.start_monitoring()
            print("Notification monitoring started")
        except Exception as e:
            print(f"Failed to start notification monitoring: {e}")
    
    def run(self, show_gui: bool = False, use_tray: bool = True):
        """Run the application."""
        self.running = True
        
        print("Starting Work Timer...")
        print(f"Database: {self.config.get_database_path()}")
        print(f"Hotkey: {self.config.get_hotkey('toggle_timer')}")
        
        # Start background services
        self.start_background_services()
        
        # Determine UI mode
        gui_settings = self.config.get_gui_settings()
        show_gui = show_gui or gui_settings['show_gui_on_startup']
        
        if show_gui and not use_tray:
            # GUI only mode
            print("Starting in GUI mode...")
            self.start_gui()
        elif use_tray and not show_gui:
            # Tray only mode
            print("Starting in system tray mode...")
            print("Right-click the tray icon for options")
            self.start_tray()
        elif show_gui and use_tray:
            # Both GUI and tray - start tray in background, GUI in foreground
            print("Starting in GUI + Tray mode...")
            tray_thread = threading.Thread(target=self.start_tray, daemon=True)
            tray_thread.start()
            time.sleep(1)  # Give tray time to start
            self.start_gui()
        else:
            # Headless mode - just background services
            print("Starting in headless mode...")
            print("Use hotkeys to control the timer")
            print("Press Ctrl+C to exit")
            try:
                while self.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                pass
    
    def shutdown(self):
        """Shutdown the application gracefully."""
        print("Shutting down Work Timer...")
        self.running = False
        
        # Stop hotkey listener
        if self.hotkey_manager:
            self.hotkey_manager.stop_listening()
        
        # Stop notification monitoring
        if self.notification_integration:
            self.notification_integration.stop_monitoring()
        
        # Stop system tray
        if self.tray:
            self.tray.stop()
        
        # Close GUI
        if self.gui:
            self.gui.destroy()
        
        print("Shutdown complete")
    
    def _format_duration(self, duration) -> str:
        """Format a duration as HH:MM:SS."""
        total_seconds = int(duration.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        seconds = total_seconds % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Work Timer - Track your work hours with hotkeys and notifications",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument('--gui', action='store_true',
                       help='Show GUI window on startup')
    parser.add_argument('--no-tray', action='store_true',
                       help="Don't start system tray (GUI only mode)")
    parser.add_argument('--config', metavar='FILE', default='config.ini',
                       help='Use custom config file (default: config.ini)')
    parser.add_argument('--version', action='version', version='Work Timer 1.0')
    
    args = parser.parse_args()
    
    # Create and run the application
    try:
        app = WorkTimerApp(args.config)
        app.run(show_gui=args.gui, use_tray=not args.no_tray)
    except KeyboardInterrupt:
        print("\nExiting...")
    except Exception as e:
        print(f"Application error: {e}")
        sys.exit(1)
    finally:
        # Ensure cleanup
        try:
            app.shutdown()
        except:
            pass

if __name__ == "__main__":
    main()




