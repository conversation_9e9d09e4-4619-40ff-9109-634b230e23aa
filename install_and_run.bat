@echo off
echo Work Timer Installation and Setup
echo ================================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7 or higher from https://python.org
    pause
    exit /b 1
)

echo Python found. Installing dependencies...

REM Install required packages
pip install -r requirements.txt

if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    echo You may need to run this as administrator or use a virtual environment
    pause
    exit /b 1
)

echo.
echo Dependencies installed successfully!
echo.

REM Run tests
echo Running tests...
python test_timer.py

if errorlevel 1 (
    echo WARNING: Some tests failed, but you can still try running the application
) else (
    echo All tests passed!
)

echo.
echo Starting Work Timer...
echo.
echo The application will start in system tray mode.
echo Right-click the tray icon for options.
echo Press Ctrl+Shift+T to toggle the timer.
echo.

REM Start the application
python work_timer.py

pause
