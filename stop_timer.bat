@echo off
echo Stopping Work Timer...

REM Kill all pythonw processes running work_timer_service.pyw
taskkill /f /im pythonw.exe /fi "WINDOWTITLE eq work_timer_service.pyw" 2>nul

REM Also try to kill by process name if the above doesn't work
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq pythonw.exe" /fo csv ^| find "pythonw.exe"') do (
    wmic process where "processid=%%i and commandline like '%%work_timer_service.pyw%%'" delete 2>nul
)

echo Work Timer stopped.
timeout /t 2 /nobreak >nul
