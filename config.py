import configparser
import os
from typing import Tuple, Dict, Any

class ConfigManager:
    """Manages application configuration settings."""
    
    def __init__(self, config_file: str = "config.ini"):
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self._load_default_config()
        self._load_config()
    
    def _load_default_config(self):
        """Load default configuration values."""
        self.config['HOTKEYS'] = {
            'toggle_timer': 'ctrl+shift+t',
            'show_status': 'ctrl+shift+s'
        }
        
        self.config['NOTIFICATIONS'] = {
            'notify_on_start': 'true',
            'notify_on_stop': 'true',
            'notify_weekly_summary': 'true',
            'weekly_notification_day': '4',  # Friday
            'weekly_notification_hour': '17',  # 5 PM
            'weekly_notification_minute': '0',
            'daily_summary_enabled': 'true',
            'daily_summary_hour': '18'  # 6 PM
        }
        
        self.config['WORK_WEEK'] = {
            'start_day': '0',  # Monday
            'work_days': '5',  # Monday to Friday
            'standard_hours_per_day': '8.0',
            'standard_hours_per_week': '40.0'
        }
        
        self.config['DATABASE'] = {
            'db_path': 'work_sessions.db',
            'backup_enabled': 'true',
            'backup_frequency_days': '7'
        }
        
        self.config['GUI'] = {
            'show_gui_on_startup': 'false',
            'minimize_to_tray': 'true',
            'update_interval_seconds': '1'
        }
    
    def _load_config(self):
        """Load configuration from file, creating it if it doesn't exist."""
        if os.path.exists(self.config_file):
            try:
                self.config.read(self.config_file)
            except Exception as e:
                print(f"Error loading config file: {e}")
                print("Using default configuration.")
        else:
            self.save_config()
    
    def save_config(self):
        """Save current configuration to file."""
        try:
            with open(self.config_file, 'w') as f:
                self.config.write(f)
        except Exception as e:
            print(f"Error saving config file: {e}")
    
    def get_hotkey(self, action: str) -> str:
        """Get hotkey combination for an action."""
        return self.config.get('HOTKEYS', action, fallback='')
    
    def set_hotkey(self, action: str, hotkey: str):
        """Set hotkey combination for an action."""
        if 'HOTKEYS' not in self.config:
            self.config.add_section('HOTKEYS')
        self.config.set('HOTKEYS', action, hotkey)
    
    def get_notification_settings(self) -> Dict[str, Any]:
        """Get all notification settings."""
        return {
            'notify_on_start': self.config.getboolean('NOTIFICATIONS', 'notify_on_start', fallback=True),
            'notify_on_stop': self.config.getboolean('NOTIFICATIONS', 'notify_on_stop', fallback=True),
            'notify_weekly_summary': self.config.getboolean('NOTIFICATIONS', 'notify_weekly_summary', fallback=True),
            'weekly_notification_day': self.config.getint('NOTIFICATIONS', 'weekly_notification_day', fallback=4),
            'weekly_notification_hour': self.config.getint('NOTIFICATIONS', 'weekly_notification_hour', fallback=17),
            'weekly_notification_minute': self.config.getint('NOTIFICATIONS', 'weekly_notification_minute', fallback=0),
            'daily_summary_enabled': self.config.getboolean('NOTIFICATIONS', 'daily_summary_enabled', fallback=True),
            'daily_summary_hour': self.config.getint('NOTIFICATIONS', 'daily_summary_hour', fallback=18)
        }
    
    def set_notification_settings(self, settings: Dict[str, Any]):
        """Set notification settings."""
        if 'NOTIFICATIONS' not in self.config:
            self.config.add_section('NOTIFICATIONS')
        
        for key, value in settings.items():
            self.config.set('NOTIFICATIONS', key, str(value))
    
    def get_work_week_settings(self) -> Dict[str, Any]:
        """Get work week settings."""
        return {
            'start_day': self.config.getint('WORK_WEEK', 'start_day', fallback=0),
            'work_days': self.config.getint('WORK_WEEK', 'work_days', fallback=5),
            'standard_hours_per_day': self.config.getfloat('WORK_WEEK', 'standard_hours_per_day', fallback=8.0),
            'standard_hours_per_week': self.config.getfloat('WORK_WEEK', 'standard_hours_per_week', fallback=40.0)
        }
    
    def set_work_week_settings(self, settings: Dict[str, Any]):
        """Set work week settings."""
        if 'WORK_WEEK' not in self.config:
            self.config.add_section('WORK_WEEK')
        
        for key, value in settings.items():
            self.config.set('WORK_WEEK', key, str(value))
    
    def get_database_path(self) -> str:
        """Get database file path."""
        return self.config.get('DATABASE', 'db_path', fallback='work_sessions.db')
    
    def set_database_path(self, path: str):
        """Set database file path."""
        if 'DATABASE' not in self.config:
            self.config.add_section('DATABASE')
        self.config.set('DATABASE', 'db_path', path)
    
    def get_gui_settings(self) -> Dict[str, Any]:
        """Get GUI settings."""
        return {
            'show_gui_on_startup': self.config.getboolean('GUI', 'show_gui_on_startup', fallback=False),
            'minimize_to_tray': self.config.getboolean('GUI', 'minimize_to_tray', fallback=True),
            'update_interval_seconds': self.config.getint('GUI', 'update_interval_seconds', fallback=1)
        }
    
    def set_gui_settings(self, settings: Dict[str, Any]):
        """Set GUI settings."""
        if 'GUI' not in self.config:
            self.config.add_section('GUI')
        
        for key, value in settings.items():
            self.config.set('GUI', key, str(value))
    
    def get_weekly_notification_time(self) -> Tuple[int, int]:
        """Get weekly notification time as (hour, minute)."""
        hour = self.config.getint('NOTIFICATIONS', 'weekly_notification_hour', fallback=17)
        minute = self.config.getint('NOTIFICATIONS', 'weekly_notification_minute', fallback=0)
        return (hour, minute)
    
    def set_weekly_notification_time(self, hour: int, minute: int):
        """Set weekly notification time."""
        if 'NOTIFICATIONS' not in self.config:
            self.config.add_section('NOTIFICATIONS')
        
        self.config.set('NOTIFICATIONS', 'weekly_notification_hour', str(hour))
        self.config.set('NOTIFICATIONS', 'weekly_notification_minute', str(minute))
    
    def reset_to_defaults(self):
        """Reset all settings to default values."""
        self.config.clear()
        self._load_default_config()
        self.save_config()
    
    def export_config(self, export_path: str):
        """Export configuration to a file."""
        try:
            with open(export_path, 'w') as f:
                self.config.write(f)
            return True
        except Exception as e:
            print(f"Error exporting config: {e}")
            return False
    
    def import_config(self, import_path: str):
        """Import configuration from a file."""
        if not os.path.exists(import_path):
            return False
        
        try:
            temp_config = configparser.ConfigParser()
            temp_config.read(import_path)
            
            # Validate the imported config has required sections
            required_sections = ['HOTKEYS', 'NOTIFICATIONS', 'WORK_WEEK', 'DATABASE', 'GUI']
            for section in required_sections:
                if section not in temp_config:
                    print(f"Warning: Missing section '{section}' in imported config")
            
            # If validation passes, use the imported config
            self.config = temp_config
            self.save_config()
            return True
            
        except Exception as e:
            print(f"Error importing config: {e}")
            return False

# Test function
def test_config():
    """Test the configuration manager."""
    config = ConfigManager("test_config.ini")
    
    print("Default hotkey:", config.get_hotkey('toggle_timer'))
    print("Notification settings:", config.get_notification_settings())
    print("Work week settings:", config.get_work_week_settings())
    
    # Test setting values
    config.set_hotkey('toggle_timer', 'ctrl+alt+t')
    config.set_weekly_notification_time(18, 30)
    config.save_config()
    
    print("Updated hotkey:", config.get_hotkey('toggle_timer'))
    print("Updated notification time:", config.get_weekly_notification_time())
    
    # Clean up test file
    import os
    if os.path.exists("test_config.ini"):
        os.remove("test_config.ini")

if __name__ == "__main__":
    test_config()
