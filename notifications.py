import datetime
from plyer import notification
import threading
import time
from typing import Optional
from timer_core import WorkTimer
from reporting import WeeklyReporter

class NotificationManager:
    """Manages system notifications for the work timer."""
    
    def __init__(self, timer: WorkTimer):
        self.timer = timer
        self.reporter = WeeklyReporter(timer)
        self.app_name = "Work Timer"
        self.app_icon = None  # Can be set to an icon path
        
        # Notification settings
        self.notify_on_start = True
        self.notify_on_stop = True
        self.notify_weekly_summary = True
        self.weekly_notification_day = 4  # Friday (0=Monday)
        self.weekly_notification_time = (17, 0)  # 5:00 PM
        
        # Background thread for scheduled notifications
        self._scheduler_thread = None
        self._stop_scheduler = threading.Event()
    
    def show_timer_started(self):
        """Show notification when timer is started."""
        if not self.notify_on_start:
            return
        
        try:
            notification.notify(
                title="Work Timer Started",
                message="Your work session has begun. Good luck!",
                app_name=self.app_name,
                app_icon=self.app_icon,
                timeout=3
            )
        except Exception as e:
            print(f"Failed to show start notification: {e}")
    
    def show_timer_stopped(self, session_duration: datetime.timedelta):
        """Show notification when timer is stopped."""
        if not self.notify_on_stop:
            return
        
        try:
            duration_str = self._format_duration(session_duration)
            today_total = self.timer.get_today_total()
            today_str = self._format_duration(today_total)
            
            message = f"Session completed: {duration_str}\nToday's total: {today_str}"
            
            notification.notify(
                title="Work Timer Stopped",
                message=message,
                app_name=self.app_name,
                app_icon=self.app_icon,
                timeout=5
            )
        except Exception as e:
            print(f"Failed to show stop notification: {e}")
    
    def show_weekly_summary(self, week_start: datetime.date = None):
        """Show weekly summary notification."""
        if not self.notify_weekly_summary:
            return
        
        try:
            summary = self.reporter.get_week_summary(week_start)
            total_hours = summary['total_hours'].total_seconds() / 3600
            working_days = summary['working_days']
            
            title = "Weekly Work Summary"
            message = f"Total: {total_hours:.1f} hours\nWorking days: {working_days}\nReady for timesheet!"
            
            notification.notify(
                title=title,
                message=message,
                app_name=self.app_name,
                app_icon=self.app_icon,
                timeout=10
            )
        except Exception as e:
            print(f"Failed to show weekly summary notification: {e}")
    
    def show_daily_summary(self):
        """Show end-of-day summary notification."""
        try:
            today_total = self.timer.get_today_total()
            hours = today_total.total_seconds() / 3600
            
            if hours > 0:
                title = "Daily Work Summary"
                message = f"Today's total: {hours:.1f} hours\nGreat work!"
                
                notification.notify(
                    title=title,
                    message=message,
                    app_name=self.app_name,
                    app_icon=self.app_icon,
                    timeout=5
                )
        except Exception as e:
            print(f"Failed to show daily summary notification: {e}")
    
    def show_custom_notification(self, title: str, message: str, timeout: int = 5):
        """Show a custom notification."""
        try:
            notification.notify(
                title=title,
                message=message,
                app_name=self.app_name,
                app_icon=self.app_icon,
                timeout=timeout
            )
        except Exception as e:
            print(f"Failed to show custom notification: {e}")
    
    def start_scheduler(self):
        """Start the background scheduler for automatic notifications."""
        if self._scheduler_thread and self._scheduler_thread.is_alive():
            return
        
        self._stop_scheduler.clear()
        self._scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self._scheduler_thread.start()
    
    def stop_scheduler(self):
        """Stop the background scheduler."""
        self._stop_scheduler.set()
        if self._scheduler_thread and self._scheduler_thread.is_alive():
            self._scheduler_thread.join(timeout=1.0)
    
    def _scheduler_loop(self):
        """Main loop for the notification scheduler."""
        last_weekly_check = None
        last_daily_check = None
        
        while not self._stop_scheduler.is_set():
            try:
                now = datetime.datetime.now()
                today = now.date()
                
                # Check for weekly notification (Friday at 5 PM)
                if (now.weekday() == self.weekly_notification_day and
                    now.hour == self.weekly_notification_time[0] and
                    now.minute == self.weekly_notification_time[1] and
                    last_weekly_check != today):
                    
                    self.show_weekly_summary()
                    last_weekly_check = today
                
                # Check for daily summary (at 6 PM)
                if (now.hour == 18 and now.minute == 0 and
                    last_daily_check != today):
                    
                    self.show_daily_summary()
                    last_daily_check = today
                
                # Sleep for a minute before next check
                self._stop_scheduler.wait(60)
                
            except Exception as e:
                print(f"Error in notification scheduler: {e}")
                self._stop_scheduler.wait(60)
    
    def _format_duration(self, duration: datetime.timedelta) -> str:
        """Format a duration as HH:MM."""
        total_seconds = int(duration.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        return f"{hours:02d}:{minutes:02d}"
    
    def set_notification_preferences(self, 
                                   notify_on_start: bool = None,
                                   notify_on_stop: bool = None,
                                   notify_weekly_summary: bool = None,
                                   weekly_day: int = None,
                                   weekly_time: tuple = None):
        """Update notification preferences."""
        if notify_on_start is not None:
            self.notify_on_start = notify_on_start
        
        if notify_on_stop is not None:
            self.notify_on_stop = notify_on_stop
        
        if notify_weekly_summary is not None:
            self.notify_weekly_summary = notify_weekly_summary
        
        if weekly_day is not None:
            self.weekly_notification_day = weekly_day
        
        if weekly_time is not None:
            self.weekly_notification_time = weekly_time

class TimerNotificationIntegration:
    """Integration class that connects timer events to notifications."""
    
    def __init__(self, timer: WorkTimer, notification_manager: NotificationManager):
        self.timer = timer
        self.notifications = notification_manager
        self._last_state = self.timer.is_running()
        self._monitoring = False
        self._monitor_thread = None
        self._stop_monitor = threading.Event()
    
    def start_monitoring(self):
        """Start monitoring timer state changes for notifications."""
        if self._monitoring:
            return
        
        self._monitoring = True
        self._stop_monitor.clear()
        self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self._monitor_thread.start()
        
        # Also start the notification scheduler
        self.notifications.start_scheduler()
    
    def stop_monitoring(self):
        """Stop monitoring timer state changes."""
        self._monitoring = False
        self._stop_monitor.set()
        
        if self._monitor_thread and self._monitor_thread.is_alive():
            self._monitor_thread.join(timeout=1.0)
        
        self.notifications.stop_scheduler()
    
    def _monitor_loop(self):
        """Monitor timer state and trigger notifications."""
        while not self._stop_monitor.is_set():
            try:
                current_state = self.timer.is_running()
                
                # Check for state changes
                if current_state != self._last_state:
                    if current_state:
                        # Timer was started
                        self.notifications.show_timer_started()
                    else:
                        # Timer was stopped - get the last completed session
                        recent_sessions = self.timer.get_recent_sessions(1)
                        if recent_sessions:
                            last_session = recent_sessions[0]
                            self.notifications.show_timer_stopped(last_session.duration)
                    
                    self._last_state = current_state
                
                # Sleep for a short interval
                self._stop_monitor.wait(2)
                
            except Exception as e:
                print(f"Error in timer monitoring: {e}")
                self._stop_monitor.wait(5)

# Test function
def test_notifications():
    """Test the notification system."""
    timer = WorkTimer()
    notifications = NotificationManager(timer)
    
    print("Testing notifications...")
    
    # Test basic notifications
    notifications.show_custom_notification("Test", "This is a test notification")
    time.sleep(2)
    
    notifications.show_timer_started()
    time.sleep(2)
    
    notifications.show_timer_stopped(datetime.timedelta(hours=1, minutes=30))
    time.sleep(2)
    
    notifications.show_weekly_summary()
    
    print("Notification tests completed")

if __name__ == "__main__":
    test_notifications()
