import tkinter as tk
from tkinter import ttk, messagebox
import datetime
import sqlite3
from typing import Optional, <PERSON><PERSON>
from timer_core import WorkTimer, WorkSession

class ManualTimeEntry:
    """Handles manual entry of work sessions for missed time."""
    
    def __init__(self, timer: WorkTimer):
        self.timer = timer
    
    def add_manual_session(self, date: datetime.date, start_time: datetime.time, 
                          end_time: datetime.time, description: str = "") -> bool:
        """Add a manual work session to the database."""
        try:
            # Combine date and time
            start_datetime = datetime.datetime.combine(date, start_time)
            end_datetime = datetime.datetime.combine(date, end_time)
            
            # Validate times
            if end_datetime <= start_datetime:
                raise ValueError("End time must be after start time")
            
            # Calculate duration
            duration = end_datetime - start_datetime
            duration_seconds = int(duration.total_seconds())
            
            # Add to database
            with sqlite3.connect(self.timer.db_path) as conn:
                cursor = conn.execute("""
                    INSERT INTO work_sessions (start_time, end_time, duration_seconds, description, manual_entry) 
                    VALUES (?, ?, ?, ?, ?)
                """, (start_datetime.isoformat(), end_datetime.isoformat(), 
                     duration_seconds, description, True))
                conn.commit()
            
            return True
            
        except Exception as e:
            print(f"Error adding manual session: {e}")
            return False
    
    def add_manual_duration(self, date: datetime.date, hours: float, description: str = "") -> bool:
        """Add a manual work session with a specific duration in hours."""
        try:
            # Default start time at 9 AM
            start_time = datetime.time(9, 0)
            
            # Calculate end time based on duration
            start_datetime = datetime.datetime.combine(date, start_time)
            hours_int = int(hours)
            minutes = int((hours - hours_int) * 60)
            end_datetime = start_datetime + datetime.timedelta(hours=hours_int, minutes=minutes)
            
            # Add to database
            with sqlite3.connect(self.timer.db_path) as conn:
                duration_seconds = int((end_datetime - start_datetime).total_seconds())
                conn.execute("""
                    INSERT INTO work_sessions (start_time, end_time, duration_seconds, description, manual_entry) 
                    VALUES (?, ?, ?, ?, ?)
                """, (start_datetime.isoformat(), end_datetime.isoformat(), 
                     duration_seconds, description, True))
                conn.commit()
            
            return True
            
        except Exception as e:
            print(f"Error adding manual duration: {e}")
            return False
    
    def get_manual_sessions(self):
        """Get all manual sessions from the database."""
        try:
            with sqlite3.connect(self.timer.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("""
                    SELECT id, start_time, end_time, duration_seconds, description 
                    FROM work_sessions 
                    WHERE manual_entry = 1
                    ORDER BY start_time DESC
                """)
                
                sessions = []
                for row in cursor:
                    start_time = datetime.datetime.fromisoformat(row['start_time'])
                    end_time = datetime.datetime.fromisoformat(row['end_time'])
                    duration = datetime.timedelta(seconds=row['duration_seconds'])
                    
                    sessions.append({
                        'id': row['id'],
                        'start_time': start_time,
                        'end_time': end_time,
                        'duration': duration,
                        'description': row['description']
                    })
                
                return sessions
                
        except Exception as e:
            print(f"Error getting manual sessions: {e}")
            return []

    def delete_manual_session(self, session_id: int) -> bool:
        """Delete a specific manual session by ID."""
        try:
            with sqlite3.connect(self.timer.db_path) as conn:
                conn.execute("""
                    DELETE FROM work_sessions 
                    WHERE id = ? AND manual_entry = 1
                """, (session_id,))
                conn.commit()
                return True
                
        except Exception as e:
            print(f"Error deleting manual session: {e}")
            return False

    def delete_all_manual_sessions(self, date: datetime.date = None) -> int:
        """Delete all manual sessions for a specific date or all manual entries if date is None.
        Returns the number of sessions deleted."""
        try:
            with sqlite3.connect(self.timer.db_path) as conn:
                if date:
                    start_datetime = datetime.datetime.combine(date, datetime.time.min)
                    end_datetime = datetime.datetime.combine(date, datetime.time.max)
                    cursor = conn.execute("""
                        DELETE FROM work_sessions 
                        WHERE manual_entry = 1 AND start_time >= ? AND start_time <= ?
                    """, (start_datetime.isoformat(), end_datetime.isoformat()))
                else:
                    cursor = conn.execute("DELETE FROM work_sessions WHERE manual_entry = 1")
                
                deleted_count = cursor.rowcount
                conn.commit()
                return deleted_count
                
        except Exception as e:
            print(f"Error deleting manual sessions: {e}")
            return 0

class ManualEntryGUI:
    """GUI for manual time entry."""
    
    def __init__(self, timer: WorkTimer, parent=None):
        self.timer = timer
        self.manual_entry = ManualTimeEntry(timer)
        self.parent = parent
        self.window = None
    
    def show_entry_dialog(self):
        """Show the manual entry dialog."""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return
        
        self.window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.window.title("Add Manual Work Time")
        self.window.geometry("450x400")
        self.window.resizable(False, False)
        
        # Center the window
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (self.window.winfo_width() // 2)
        y = (self.window.winfo_screenheight() // 2) - (self.window.winfo_height() // 2)
        self.window.geometry(f"+{x}+{y}")
        
        self._create_entry_form()
        self._create_history_section()
    
    def _create_entry_form(self):
        """Create the manual entry form."""
        # Main frame
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="Add Manual Work Time", 
                               font=("Arial", 14, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Entry method selection
        method_frame = ttk.LabelFrame(main_frame, text="Entry Method", padding="10")
        method_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.entry_method = tk.StringVar(value="duration")
        ttk.Radiobutton(method_frame, text="Enter duration (hours)", 
                       variable=self.entry_method, value="duration",
                       command=self._toggle_entry_method).grid(row=0, column=0, sticky=tk.W)
        ttk.Radiobutton(method_frame, text="Enter start/end times", 
                       variable=self.entry_method, value="times",
                       command=self._toggle_entry_method).grid(row=0, column=1, sticky=tk.W)
        
        # Date selection
        date_frame = ttk.Frame(main_frame)
        date_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(date_frame, text="Date:").grid(row=0, column=0, sticky=tk.W)
        self.date_var = tk.StringVar(value=datetime.date.today().strftime("%Y-%m-%d"))
        self.date_entry = ttk.Entry(date_frame, textvariable=self.date_var, width=12)
        self.date_entry.grid(row=0, column=1, padx=(10, 0))
        
        # Date picker button
        ttk.Button(date_frame, text="📅", width=3, 
                  command=self._show_date_picker).grid(row=0, column=2, padx=(5, 0))
        
        ttk.Button(date_frame, text="Today", 
                  command=lambda: self.date_var.set(datetime.date.today().strftime("%Y-%m-%d"))
                  ).grid(row=0, column=3, padx=(5, 0))
        
        # Duration entry (default)
        self.duration_frame = ttk.LabelFrame(main_frame, text="Duration", padding="10")
        self.duration_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(self.duration_frame, text="Hours worked:").grid(row=0, column=0, sticky=tk.W)
        self.duration_var = tk.StringVar()
        duration_entry = ttk.Entry(self.duration_frame, textvariable=self.duration_var, width=10)
        duration_entry.grid(row=0, column=1, padx=(10, 0))
        
        # Quick duration buttons
        quick_frame = ttk.Frame(self.duration_frame)
        quick_frame.grid(row=1, column=0, columnspan=2, pady=(10, 0))
        
        for hours in [1, 2, 4, 8]:
            ttk.Button(quick_frame, text=f"{hours}h", width=5,
                      command=lambda h=hours: self.duration_var.set(str(h))
                      ).pack(side=tk.LEFT, padx=2)
        
        # Time entry (hidden by default)
        self.time_frame = ttk.LabelFrame(main_frame, text="Start/End Times", padding="10")
        
        ttk.Label(self.time_frame, text="Start time:").grid(row=0, column=0, sticky=tk.W)
        self.start_time_var = tk.StringVar(value="09:00")
        ttk.Entry(self.time_frame, textvariable=self.start_time_var, width=8).grid(row=0, column=1, padx=(10, 0))
        
        ttk.Label(self.time_frame, text="End time:").grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        self.end_time_var = tk.StringVar(value="17:00")
        ttk.Entry(self.time_frame, textvariable=self.end_time_var, width=8).grid(row=1, column=1, padx=(10, 0), pady=(5, 0))
        
        # Description
        desc_frame = ttk.LabelFrame(main_frame, text="Description (Optional)", padding="10")
        desc_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.description_var = tk.StringVar()
        ttk.Entry(desc_frame, textvariable=self.description_var, width=40).pack()
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=6, column=0, columnspan=3, pady=(10, 0))
        
        ttk.Button(button_frame, text="Add Time", command=self._add_time).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Cancel", command=self.window.destroy).pack(side=tk.LEFT)
        
        # Configure grid weights
        main_frame.columnconfigure(1, weight=1)

    def _show_date_picker(self):
        """Show a calendar date picker."""
        try:
            from tkcalendar import Calendar
            
            top = tk.Toplevel(self.window)
            top.title("Select Date")
            
            # Parse current date
            try:
                current_date = datetime.datetime.strptime(self.date_var.get(), "%Y-%m-%d").date()
            except:
                current_date = datetime.date.today()
            
            cal = Calendar(top, selectmode='day', 
                          year=current_date.year, 
                          month=current_date.month,
                          day=current_date.day)
            cal.pack(padx=10, pady=10)
            
            def set_date():
                selected_date = cal.selection_get()
                self.date_var.set(selected_date.strftime("%Y-%m-%d"))
                top.destroy()
            
            ttk.Button(top, text="Select", command=set_date).pack(pady=10)
            
        except ImportError:
            messagebox.showinfo("Info", "Please install tkcalendar: pip install tkcalendar")

    def _create_history_section(self):
        """Create the manual entry history section."""
        history_frame = ttk.LabelFrame(self.window, text="Manual Entry History", padding="10")
        history_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))
        
        # Controls for history
        controls_frame = ttk.Frame(history_frame)
        controls_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(controls_frame, text="Refresh", 
                  command=self._refresh_history).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(controls_frame, text="Delete Selected", 
                  command=self._delete_selected).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(controls_frame, text="Clear All Manual Entries", 
                  command=self._clear_all_entries).pack(side=tk.LEFT)
        
        # History list
        self.history_tree = ttk.Treeview(history_frame, columns=("date", "time", "duration", "desc"), 
                                        show="headings", height=6)
        self.history_tree.heading("date", text="Date")
        self.history_tree.heading("time", text="Time")
        self.history_tree.heading("duration", text="Duration")
        self.history_tree.heading("desc", text="Description")
        
        self.history_tree.column("date", width=100)
        self.history_tree.column("time", width=100)
        self.history_tree.column("duration", width=80)
        self.history_tree.column("desc", width=150)
        
        self.history_tree.pack(fill=tk.BOTH, expand=True)
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Load initial history
        self._refresh_history()

    def _refresh_history(self):
        """Refresh the history list."""
        # Clear existing items
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)
        
        # Get manual sessions
        sessions = self.manual_entry.get_manual_sessions()
        
        # Add to treeview
        for session in sessions:
            date_str = session['start_time'].strftime("%Y-%m-%d")
            time_str = f"{session['start_time'].strftime('%H:%M')} - {session['end_time'].strftime('%H:%M')}"
            duration_str = self._format_duration(session['duration'])
            
            self.history_tree.insert("", "end", values=(date_str, time_str, duration_str, session['description']),
                                   iid=str(session['id']))

    def _delete_selected(self):
        """Delete selected manual entries."""
        selected = self.history_tree.selection()
        if not selected:
            messagebox.showinfo("Info", "No entries selected")
            return
        
        if messagebox.askyesno("Confirm", f"Delete {len(selected)} selected entries?"):
            deleted_count = 0
            for item_id in selected:
                session_id = int(item_id)
                if self.manual_entry.delete_manual_session(session_id):
                    deleted_count += 1
        
            self._refresh_history()
            messagebox.showinfo("Success", f"Deleted {deleted_count} entries")

    def _clear_all_entries(self):
        """Clear all manual time entries."""
        if messagebox.askyesno("Confirm", "Delete ALL manual time entries?\nThis cannot be undone!"):
            deleted_count = self.manual_entry.delete_all_manual_sessions()
            self._refresh_history()
            messagebox.showinfo("Success", f"Deleted {deleted_count} manual time entries")

    def _format_duration(self, duration):
        """Format duration as HH:MM."""
        total_seconds = int(duration.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        return f"{hours}h {minutes:02d}m"

    def _toggle_entry_method(self):
        """Toggle between duration and time entry methods."""
        if self.entry_method.get() == "duration":
            self.time_frame.grid_remove()
            self.duration_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        else:
            self.duration_frame.grid_remove()
            self.time_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
    
    def _add_time(self):
        """Add the manual time entry."""
        try:
            # Parse date
            date_str = self.date_var.get()
            date = datetime.datetime.strptime(date_str, "%Y-%m-%d").date()
            
            description = self.description_var.get().strip()
            
            if self.entry_method.get() == "duration":
                # Duration method
                duration_str = self.duration_var.get().strip()
                if not duration_str:
                    messagebox.showerror("Error", "Please enter the number of hours worked.")
                    return
                
                try:
                    duration_hours = float(duration_str)
                    if duration_hours <= 0 or duration_hours > 24:
                        messagebox.showerror("Error", "Duration must be between 0 and 24 hours.")
                        return
                except ValueError:
                    messagebox.showerror("Error", "Please enter a valid number for hours.")
                    return
                
                success = self.manual_entry.add_manual_duration(date, duration_hours, description)
                
            else:
                # Start/end time method
                start_str = self.start_time_var.get().strip()
                end_str = self.end_time_var.get().strip()
                
                if not start_str or not end_str:
                    messagebox.showerror("Error", "Please enter both start and end times.")
                    return
                
                try:
                    start_time = datetime.datetime.strptime(start_str, "%H:%M").time()
                    end_time = datetime.datetime.strptime(end_str, "%H:%M").time()
                except ValueError:
                    messagebox.showerror("Error", "Please enter times in HH:MM format (e.g., 09:30).")
                    return
                
                success = self.manual_entry.add_manual_session(date, start_time, end_time, description)
            
            if success:
                messagebox.showinfo("Success", "Manual time entry added successfully!")
                self._clear_form()
            else:
                messagebox.showerror("Error", "Failed to add manual time entry.")
                
        except ValueError as e:
            messagebox.showerror("Error", f"Invalid date format. Please use YYYY-MM-DD format.")
        except Exception as e:
            messagebox.showerror("Error", f"An error occurred: {str(e)}")
    
    def _clear_form(self):
        """Clear the form after successful entry."""
        self.duration_var.set("")
        self.start_time_var.set("09:00")
        self.end_time_var.set("17:00")
        self.description_var.set("")

    def show_batch_entry_dialog(self):
        """Show dialog for entering time for multiple days."""
        batch_window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        batch_window.title("Batch Time Entry")
        batch_window.geometry("450x400")
        batch_window.resizable(False, False)
        
        # Center the window
        batch_window.update_idletasks()
        x = (batch_window.winfo_screenwidth() // 2) - (batch_window.winfo_width() // 2)
        y = (batch_window.winfo_screenheight() // 2) - (batch_window.winfo_height() // 2)
        batch_window.geometry(f"+{x}+{y}")
        
        # Main frame
        main_frame = ttk.Frame(batch_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="Batch Time Entry", 
                               font=("Arial", 14, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Date range selection
        date_frame = ttk.LabelFrame(main_frame, text="Date Range", padding="10")
        date_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(date_frame, text="Start Date:").grid(row=0, column=0, sticky=tk.W)
        start_date_var = tk.StringVar(value=datetime.date.today().strftime("%Y-%m-%d"))
        start_date_entry = ttk.Entry(date_frame, textvariable=start_date_var, width=12)
        start_date_entry.grid(row=0, column=1, padx=(10, 0))
        
        ttk.Label(date_frame, text="End Date:").grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        end_date_var = tk.StringVar(value=datetime.date.today().strftime("%Y-%m-%d"))
        end_date_entry = ttk.Entry(date_frame, textvariable=end_date_var, width=12)
        end_date_entry.grid(row=1, column=1, padx=(10, 0), pady=(5, 0))
        
        # Workdays only checkbox
        workdays_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(date_frame, text="Workdays only (Mon-Fri)", 
                       variable=workdays_var).grid(row=2, column=0, columnspan=2, pady=(5, 0))
        
        # Time settings
        time_frame = ttk.LabelFrame(main_frame, text="Time Settings", padding="10")
        time_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(time_frame, text="Hours per day:").grid(row=0, column=0, sticky=tk.W)
        hours_var = tk.StringVar(value="8.0")
        ttk.Entry(time_frame, textvariable=hours_var, width=8).grid(row=0, column=1, padx=(10, 0))
        
        ttk.Label(time_frame, text="Start time:").grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        start_time_var = tk.StringVar(value="09:00")
        ttk.Entry(time_frame, textvariable=start_time_var, width=8).grid(row=1, column=1, padx=(10, 0), pady=(5, 0))
        
        # Description
        desc_frame = ttk.LabelFrame(main_frame, text="Description (Optional)", padding="10")
        desc_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        description_var = tk.StringVar()
        ttk.Entry(desc_frame, textvariable=description_var, width=40).pack()
        
        # Preview
        preview_frame = ttk.LabelFrame(main_frame, text="Preview", padding="10")
        preview_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        preview_var = tk.StringVar(value="Enter date range to see preview")
        ttk.Label(preview_frame, textvariable=preview_var).pack()
        
        # Update preview when inputs change
        def update_preview(*args):
            try:
                start_date = datetime.datetime.strptime(start_date_var.get(), "%Y-%m-%d").date()
                end_date = datetime.datetime.strptime(end_date_var.get(), "%Y-%m-%d").date()
                
                if end_date < start_date:
                    preview_var.set("Error: End date must be after start date")
                    return
                
                days = (end_date - start_date).days + 1
                workdays = 0
                
                current_date = start_date
                while current_date <= end_date:
                    if not workdays_var.get() or current_date.weekday() < 5:  # Mon-Fri are 0-4
                        workdays += 1
                    current_date += datetime.timedelta(days=1)
                
                try:
                    hours = float(hours_var.get())
                    total_hours = workdays * hours
                    preview_var.set(f"Will add {workdays} days × {hours} hours = {total_hours} hours total")
                except ValueError:
                    preview_var.set(f"Will add time for {workdays} days")
            except ValueError:
                preview_var.set("Invalid date format")
        
        start_date_var.trace_add("write", update_preview)
        end_date_var.trace_add("write", update_preview)
        hours_var.trace_add("write", update_preview)
        workdays_var.trace_add("write", update_preview)
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=3, pady=(10, 0))
        
        def add_batch_time():
            try:
                start_date = datetime.datetime.strptime(start_date_var.get(), "%Y-%m-%d").date()
                end_date = datetime.datetime.strptime(end_date_var.get(), "%Y-%m-%d").date()
                
                if end_date < start_date:
                    messagebox.showerror("Error", "End date must be after start date")
                    return
                
                try:
                    hours = float(hours_var.get())
                    if hours <= 0 or hours > 24:
                        messagebox.showerror("Error", "Hours must be between 0 and 24")
                        return
                except ValueError:
                    messagebox.showerror("Error", "Invalid hours format")
                    return
                
                description = description_var.get().strip()
                
                # Add time for each day in range
                added_count = 0
                current_date = start_date
                while current_date <= end_date:
                    # Skip weekends if workdays only is checked
                    if workdays_var.get() and current_date.weekday() >= 5:  # Sat-Sun are 5-6
                        current_date += datetime.timedelta(days=1)
                        continue
                    
                    if self.manual_entry.add_manual_duration(current_date, hours, description):
                        added_count += 1
                    
                    current_date += datetime.timedelta(days=1)
                
                messagebox.showinfo("Success", f"Added time entries for {added_count} days")
                batch_window.destroy()
                
            except ValueError as e:
                messagebox.showerror("Error", f"Invalid date format: {str(e)}")
            except Exception as e:
                messagebox.showerror("Error", f"An error occurred: {str(e)}")
        
        ttk.Button(button_frame, text="Add Batch Time", command=add_batch_time).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Cancel", command=batch_window.destroy).pack(side=tk.LEFT)

# Test function
def test_manual_entry():
    """Test the manual entry functionality."""
    timer = WorkTimer("test_manual.db")
    gui = ManualEntryGUI(timer)
    gui.show_entry_dialog()
    
    if hasattr(gui.window, 'mainloop'):
        gui.window.mainloop()

if __name__ == "__main__":
    test_manual_entry()





