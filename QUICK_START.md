# Work Timer - Quick Start Guide

## 🚀 Get Started in 30 Seconds

### Option 1: Easy Install (Recommended)
1. **Double-click** `install_and_run.bat`
2. Wait for installation to complete
3. Look for the timer icon in your system tray
4. **Press `Ctrl+Shift+T`** to start timing!

### Option 2: Background Mode (No Console Window)
1. **Double-click** `start_background.bat`
2. Timer runs silently in background
3. Check system tray for the timer icon
4. **Press `Ctrl+Shift+T`** to start timing!

### Option 3: Auto-Start with Windows
1. **Right-click** `add_to_startup.bat` → "Run as administrator"
2. Timer will start automatically when you log in
3. No need to remember to start it manually!

## 🎯 Basic Usage

### Timer Control
- **Start Timer**: Press `Ctrl+Shift+T` anywhere on your computer
- **Stop Timer**: Press `Ctrl+Shift+T` again
- **Check Status**: Right-click the system tray icon

### Adding Missed Hours ⭐ NEW
1. **Right-click** the system tray icon
2. Select **"Add Manual Time"**
3. Choose your method:
   - **Duration**: Enter "2.5" for 2.5 hours
   - **Times**: Enter "9:00" to "17:30"
4. Select the date and add optional description
5. Click **"Add Time"**

### Weekly Reports
- **Right-click** tray icon → **"Weekly Report"** for detailed breakdown
- **Right-click** tray icon → **"Timesheet Summary"** for quick copy-paste
- **Automatic notifications** every Friday at 5 PM with your weekly total

## 🔧 Management

### Check if Timer is Running
- **Double-click** `check_timer_status.bat`
- Look for the timer icon in system tray (green = running, red = stopped)

### Stop the Timer
- **Right-click** tray icon → **"Quit"**
- Or **double-click** `stop_timer.bat`

### View Logs
- Check `work_timer.log` for any issues
- All your time data is safely stored in `work_sessions.db`

## 🎨 Customization

Edit `config.ini` to change:
- **Hotkey**: Change `ctrl+shift+t` to something else
- **Notifications**: Turn on/off various alerts
- **Work Week**: Set your work days and hours

## 🆘 Troubleshooting

### Timer Not Responding to Hotkey
1. Try running `start_background.bat` as administrator
2. Check if another app is using the same hotkey
3. Change hotkey in `config.ini`

### System Tray Icon Missing
1. Check Windows notification area settings
2. Run `check_timer_status.bat` to see if it's actually running
3. Try restarting: `stop_timer.bat` then `start_background.bat`

### Need Help?
- Check `work_timer.log` for error messages
- Run `test_timer.py` to verify everything works
- All your data is safely stored and won't be lost

## 📊 What You Get

### Automatic Tracking
- Every work session is automatically saved
- Resume active sessions after computer restart
- Never lose your time data

### Smart Reports
- Daily totals in system tray tooltip
- Weekly breakdown with timesheet format
- Manual entries clearly marked
- Export-ready summaries

### Professional Features
- Runs silently in background
- No command windows cluttering your screen
- Automatic Windows startup
- Configurable notifications
- Manual time entry for missed hours

---

**🎉 You're all set!** Press `Ctrl+Shift+T` to start tracking your work time!
