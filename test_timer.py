#!/usr/bin/env python3
"""
Test script for the Work Timer application.

This script tests the core functionality of the timer without requiring
GUI or system tray components.
"""

import time
import datetime
from timer_core import WorkTimer
from reporting import WeeklyReporter

def test_basic_timer_functionality():
    """Test basic timer start/stop functionality."""
    print("Testing basic timer functionality...")
    
    # Create a test timer with a temporary database
    timer = WorkTimer("test_timer.db")
    
    # Test initial state
    assert not timer.is_running(), "Timer should not be running initially"
    assert timer.get_current_session_duration() == datetime.timedelta(0), "Initial session duration should be zero"
    
    # Test starting timer
    started = timer.start_timer()
    assert started, "Timer should start successfully"
    assert timer.is_running(), "Timer should be running after start"
    
    # Wait a bit and check duration
    time.sleep(2)
    duration = timer.get_current_session_duration()
    assert duration.total_seconds() >= 2, "Duration should be at least 2 seconds"
    
    # Test stopping timer
    completed_session = timer.stop_timer()
    assert completed_session is not None, "Should return completed session"
    assert not timer.is_running(), "Timer should not be running after stop"
    assert completed_session.duration.total_seconds() >= 2, "Completed session should have duration"
    
    print("✓ Basic timer functionality works")

def test_toggle_functionality():
    """Test timer toggle functionality."""
    print("Testing toggle functionality...")
    
    timer = WorkTimer("test_timer.db")
    
    # Test toggle from stopped to started
    is_running, completed_session = timer.toggle_timer()
    assert is_running, "Toggle should start timer"
    assert completed_session is None, "No completed session when starting"
    assert timer.is_running(), "Timer should be running"
    
    time.sleep(1)
    
    # Test toggle from started to stopped
    is_running, completed_session = timer.toggle_timer()
    assert not is_running, "Toggle should stop timer"
    assert completed_session is not None, "Should have completed session when stopping"
    assert not timer.is_running(), "Timer should not be running"
    
    print("✓ Toggle functionality works")

def test_daily_totals():
    """Test daily total calculations."""
    print("Testing daily totals...")
    
    timer = WorkTimer("test_timer.db")
    
    # Start and stop a few sessions
    for i in range(3):
        timer.start_timer()
        time.sleep(1)
        timer.stop_timer()
    
    # Check today's total
    today_total = timer.get_today_total()
    assert today_total.total_seconds() >= 3, "Today's total should be at least 3 seconds"
    
    print(f"✓ Daily totals work - Today: {today_total}")

def test_reporting():
    """Test reporting functionality."""
    print("Testing reporting...")
    
    timer = WorkTimer("test_timer.db")
    reporter = WeeklyReporter(timer)
    
    # Generate reports
    summary = reporter.get_week_summary()
    assert 'total_hours' in summary, "Summary should contain total_hours"
    assert 'daily_totals' in summary, "Summary should contain daily_totals"
    
    text_report = reporter.generate_text_report()
    assert len(text_report) > 0, "Text report should not be empty"
    assert "WEEKLY WORK REPORT" in text_report, "Report should contain header"
    
    timesheet_summary = reporter.generate_timesheet_summary()
    assert len(timesheet_summary) > 0, "Timesheet summary should not be empty"
    assert "Total Hours:" in timesheet_summary, "Summary should contain total hours"
    
    print("✓ Reporting functionality works")

def test_persistence():
    """Test data persistence across timer instances."""
    print("Testing data persistence...")
    
    # Create first timer instance and add some data
    timer1 = WorkTimer("test_timer.db")
    timer1.start_timer()
    time.sleep(1)
    completed_session = timer1.stop_timer()
    
    original_duration = completed_session.duration
    
    # Create second timer instance and check data persists
    timer2 = WorkTimer("test_timer.db")
    today_total = timer2.get_today_total()
    
    assert today_total >= original_duration, "Data should persist across instances"
    
    print("✓ Data persistence works")

def cleanup_test_files():
    """Clean up test database files."""
    import os
    test_files = ["test_timer.db"]
    
    for file in test_files:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"Cleaned up {file}")
            except Exception as e:
                print(f"Could not remove {file}: {e}")

def run_all_tests():
    """Run all tests."""
    print("=" * 50)
    print("Work Timer Test Suite")
    print("=" * 50)
    
    try:
        test_basic_timer_functionality()
        test_toggle_functionality()
        test_daily_totals()
        test_reporting()
        test_persistence()
        
        print("\n" + "=" * 50)
        print("✓ All tests passed!")
        print("=" * 50)
        
    except AssertionError as e:
        print(f"\n✗ Test failed: {e}")
        return False
    except Exception as e:
        print(f"\n✗ Test error: {e}")
        return False
    finally:
        cleanup_test_files()
    
    return True

if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
