import pystray
from pystray import MenuItem, Menu
from PIL import Image, ImageDraw
import threading
import datetime
from timer_core import WorkTimer
from notifications import NotificationManager
from reporting import WeeklyReporter
import tkinter as tk
from tkinter import messagebox, scrolledtext

class SystemTrayManager:
    """Manages the system tray icon and menu for the work timer."""
    
    def __init__(self, timer: WorkTimer, notification_manager: NotificationManager):
        self.timer = timer
        self.notifications = notification_manager
        self.reporter = WeeklyReporter(timer)
        self.icon = None
        self.running = False
        
        # Create icons
        self.stopped_icon = self._create_icon("red")
        self.running_icon = self._create_icon("green")
    
    def _create_icon(self, color: str) -> Image.Image:
        """Create a simple colored circle icon."""
        # Create a 64x64 image
        image = Image.new('RGBA', (64, 64), (255, 255, 255, 0))
        draw = ImageDraw.Draw(image)
        
        # Draw a circle
        if color == "red":
            fill_color = (220, 50, 50, 255)
        elif color == "green":
            fill_color = (50, 220, 50, 255)
        else:
            fill_color = (128, 128, 128, 255)
        
        draw.ellipse([8, 8, 56, 56], fill=fill_color, outline=(0, 0, 0, 255), width=2)
        
        # Add a small "T" for Timer
        draw.text((24, 20), "T", fill=(255, 255, 255, 255))
        
        return image
    
    def _update_icon(self):
        """Update the tray icon based on timer state."""
        if self.icon:
            if self.timer.is_running():
                self.icon.icon = self.running_icon
                current_duration = self.timer.get_current_session_duration()
                duration_str = self._format_duration(current_duration)
                self.icon.title = f"Work Timer - Running ({duration_str})"
            else:
                self.icon.icon = self.stopped_icon
                today_total = self.timer.get_today_total()
                total_str = self._format_duration(today_total)
                self.icon.title = f"Work Timer - Stopped (Today: {total_str})"
    
    def _toggle_timer(self, icon, item):
        """Toggle the timer state."""
        try:
            is_running, completed_session = self.timer.toggle_timer()
            
            if is_running:
                self.notifications.show_timer_started()
            else:
                if completed_session:
                    self.notifications.show_timer_stopped(completed_session.duration)
            
            self._update_icon()
            
        except Exception as e:
            self._show_error("Timer Error", f"Failed to toggle timer: {str(e)}")
    
    def _show_status(self, icon, item):
        """Show current status in a message box."""
        try:
            status = "RUNNING" if self.timer.is_running() else "STOPPED"
            current_duration = self.timer.get_current_session_duration()
            today_total = self.timer.get_today_total()
            week_total = self.timer.get_week_total()
            
            current_str = self._format_duration(current_duration)
            today_str = self._format_duration(today_total)
            week_str = self._format_duration(week_total)
            
            message = f"""Timer Status: {status}
Current Session: {current_str}
Today's Total: {today_str}
This Week: {week_str}"""
            
            self._show_message("Work Timer Status", message)
            
        except Exception as e:
            self._show_error("Status Error", f"Failed to get status: {str(e)}")
    
    def _show_weekly_report(self, icon, item):
        """Show the weekly report in a new window."""
        try:
            report_text = self.reporter.generate_text_report()
            self._show_report_window("Weekly Report", report_text)
            
        except Exception as e:
            self._show_error("Report Error", f"Failed to generate report: {str(e)}")
    
    def _show_timesheet_summary(self, icon, item):
        """Show timesheet summary."""
        try:
            summary = self.reporter.generate_timesheet_summary()
            self._show_message("Timesheet Summary", summary)
            
        except Exception as e:
            self._show_error("Summary Error", f"Failed to generate summary: {str(e)}")
    
    def _quit_application(self, icon, item):
        """Quit the application."""
        self.stop()
    
    def _create_menu(self):
        """Create the context menu for the tray icon."""
        return Menu(
            MenuItem("Toggle Timer", self._toggle_timer, default=True),
            MenuItem("Show Status", self._show_status),
            Menu.SEPARATOR,
            MenuItem("Weekly Report", self._show_weekly_report),
            MenuItem("Timesheet Summary", self._show_timesheet_summary),
            Menu.SEPARATOR,
            MenuItem("Quit", self._quit_application)
        )
    
    def start(self):
        """Start the system tray icon."""
        if self.running:
            return
        
        self.running = True
        
        # Create the tray icon
        self.icon = pystray.Icon(
            "work_timer",
            self.stopped_icon,
            "Work Timer",
            self._create_menu()
        )
        
        # Start icon update thread
        self._start_update_thread()
        
        # Run the icon (this blocks)
        self.icon.run()
    
    def stop(self):
        """Stop the system tray icon."""
        self.running = False
        if self.icon:
            self.icon.stop()
    
    def _start_update_thread(self):
        """Start a thread to periodically update the icon."""
        def update_loop():
            while self.running:
                try:
                    self._update_icon()
                    threading.Event().wait(5)  # Update every 5 seconds
                except Exception as e:
                    print(f"Error updating tray icon: {e}")
                    threading.Event().wait(10)
        
        update_thread = threading.Thread(target=update_loop, daemon=True)
        update_thread.start()
    
    def _format_duration(self, duration: datetime.timedelta) -> str:
        """Format a duration as HH:MM."""
        total_seconds = int(duration.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        return f"{hours:02d}:{minutes:02d}"
    
    def _show_message(self, title: str, message: str):
        """Show a message box."""
        def show():
            root = tk.Tk()
            root.withdraw()  # Hide the main window
            messagebox.showinfo(title, message)
            root.destroy()
        
        # Run in a separate thread to avoid blocking
        threading.Thread(target=show, daemon=True).start()
    
    def _show_error(self, title: str, message: str):
        """Show an error message box."""
        def show():
            root = tk.Tk()
            root.withdraw()  # Hide the main window
            messagebox.showerror(title, message)
            root.destroy()
        
        # Run in a separate thread to avoid blocking
        threading.Thread(target=show, daemon=True).start()
    
    def _show_report_window(self, title: str, content: str):
        """Show a report in a new window."""
        def show():
            root = tk.Tk()
            root.title(title)
            root.geometry("600x500")
            
            # Create scrolled text widget
            text_widget = scrolledtext.ScrolledText(root, wrap=tk.WORD, font=("Courier", 10))
            text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # Insert content
            text_widget.insert(tk.END, content)
            text_widget.config(state=tk.DISABLED)
            
            # Add close button
            close_button = tk.Button(root, text="Close", command=root.destroy)
            close_button.pack(pady=5)
            
            # Center the window
            root.update_idletasks()
            x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
            y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
            root.geometry(f"+{x}+{y}")
            
            root.mainloop()
        
        # Run in a separate thread
        threading.Thread(target=show, daemon=True).start()

# Test function
def test_system_tray():
    """Test the system tray functionality."""
    from timer_core import WorkTimer
    from notifications import NotificationManager
    
    timer = WorkTimer()
    notifications = NotificationManager(timer)
    tray = SystemTrayManager(timer, notifications)
    
    print("Starting system tray... Right-click the tray icon to see options.")
    print("Press Ctrl+C to exit.")
    
    try:
        tray.start()
    except KeyboardInterrupt:
        print("\nStopping system tray...")
        tray.stop()

if __name__ == "__main__":
    test_system_tray()
