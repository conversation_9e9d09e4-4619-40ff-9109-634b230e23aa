import datetime
from typing import Dict, List, Tuple
from timer_core import WorkTimer, WorkSession

class WeeklyReporter:
    """Handles weekly reporting and time calculations."""
    
    def __init__(self, timer: WorkTimer):
        self.timer = timer
    
    def get_week_summary(self, week_start: datetime.date = None) -> Dict:
        """Get a comprehensive summary of the week's work."""
        if week_start is None:
            today = datetime.date.today()
            week_start = today - datetime.timedelta(days=today.weekday())
        
        week_end = week_start + datetime.timedelta(days=6)
        
        summary = {
            'week_start': week_start,
            'week_end': week_end,
            'daily_totals': {},
            'total_hours': datetime.timedelta(0),
            'average_daily_hours': datetime.timedelta(0),
            'working_days': 0,
            'sessions_count': 0,
            'longest_session': datetime.timedelta(0),
            'shortest_session': None
        }
        
        all_sessions = []
        
        # Calculate daily totals
        for i in range(7):
            current_date = week_start + datetime.timedelta(days=i)
            day_total = self.timer.get_date_total(current_date)
            day_name = current_date.strftime('%A')
            
            summary['daily_totals'][day_name] = {
                'date': current_date,
                'total': day_total,
                'formatted': self._format_duration(day_total)
            }
            
            summary['total_hours'] += day_total
            
            if day_total > datetime.timedelta(0):
                summary['working_days'] += 1
            
            # Get sessions for this day
            day_sessions = self._get_day_sessions(current_date)
            all_sessions.extend(day_sessions)
        
        # Calculate session statistics
        summary['sessions_count'] = len(all_sessions)
        
        if all_sessions:
            durations = [session.duration for session in all_sessions]
            summary['longest_session'] = max(durations)
            summary['shortest_session'] = min(durations)
        
        # Calculate average daily hours
        if summary['working_days'] > 0:
            total_seconds = summary['total_hours'].total_seconds()
            avg_seconds = total_seconds / summary['working_days']
            summary['average_daily_hours'] = datetime.timedelta(seconds=avg_seconds)
        
        return summary
    
    def _get_day_sessions(self, date: datetime.date) -> List[WorkSession]:
        """Get all completed sessions for a specific day."""
        start_of_day = datetime.datetime.combine(date, datetime.time.min)
        end_of_day = datetime.datetime.combine(date, datetime.time.max)
        
        sessions = []
        import sqlite3
        
        with sqlite3.connect(self.timer.db_path) as conn:
            cursor = conn.execute("""
                SELECT id, start_time, end_time FROM work_sessions 
                WHERE start_time >= ? AND start_time <= ? AND end_time IS NOT NULL
                ORDER BY start_time
            """, (start_of_day.isoformat(), end_of_day.isoformat()))
            
            for row in cursor:
                session_id, start_time_str, end_time_str = row
                start_time = datetime.datetime.fromisoformat(start_time_str)
                end_time = datetime.datetime.fromisoformat(end_time_str)
                sessions.append(WorkSession(start_time, end_time, session_id))
        
        return sessions
    
    def generate_text_report(self, week_start: datetime.date = None) -> str:
        """Generate a formatted text report for the week."""
        summary = self.get_week_summary(week_start)
        
        report = []
        report.append("WEEKLY WORK REPORT")
        report.append("=" * 50)
        report.append(f"Week of {summary['week_start'].strftime('%B %d, %Y')} to {summary['week_end'].strftime('%B %d, %Y')}")
        report.append("")
        
        # Daily breakdown
        report.append("DAILY BREAKDOWN:")
        report.append("-" * 30)
        
        for day_name in ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']:
            if day_name in summary['daily_totals']:
                day_data = summary['daily_totals'][day_name]
                date_str = day_data['date'].strftime('%m/%d')
                time_str = day_data['formatted']
                
                if day_data['date'] == datetime.date.today():
                    report.append(f"{day_name:10} {date_str}  {time_str} (Today)")
                else:
                    report.append(f"{day_name:10} {date_str}  {time_str}")
        
        report.append("-" * 30)
        report.append(f"{'TOTAL:':10}       {self._format_duration(summary['total_hours'])}")
        report.append("")
        
        # Summary statistics
        report.append("SUMMARY:")
        report.append("-" * 20)
        report.append(f"Total Hours: {self._format_duration(summary['total_hours'])}")
        report.append(f"Working Days: {summary['working_days']}")
        report.append(f"Average Daily Hours: {self._format_duration(summary['average_daily_hours'])}")
        report.append(f"Total Sessions: {summary['sessions_count']}")
        
        if summary['longest_session']:
            report.append(f"Longest Session: {self._format_duration(summary['longest_session'])}")
        
        if summary['shortest_session']:
            report.append(f"Shortest Session: {self._format_duration(summary['shortest_session'])}")
        
        report.append("")
        
        # Timesheet format
        report.append("TIMESHEET FORMAT:")
        report.append("-" * 25)
        total_hours = summary['total_hours'].total_seconds() / 3600
        report.append(f"Total Hours: {total_hours:.2f}")
        
        return "\n".join(report)
    
    def generate_timesheet_summary(self, week_start: datetime.date = None) -> str:
        """Generate a concise summary suitable for timesheet entry."""
        summary = self.get_week_summary(week_start)
        
        total_hours = summary['total_hours'].total_seconds() / 3600
        working_days = summary['working_days']
        
        timesheet = []
        timesheet.append(f"Week of {summary['week_start'].strftime('%m/%d/%Y')}")
        timesheet.append(f"Total Hours: {total_hours:.2f}")
        timesheet.append(f"Working Days: {working_days}")
        
        if working_days > 0:
            avg_hours = total_hours / working_days
            timesheet.append(f"Average Daily: {avg_hours:.2f} hours")
        
        return "\n".join(timesheet)
    
    def get_monthly_summary(self, year: int = None, month: int = None) -> Dict:
        """Get a summary for an entire month."""
        if year is None or month is None:
            today = datetime.date.today()
            year = today.year
            month = today.month
        
        # Get first and last day of month
        first_day = datetime.date(year, month, 1)
        if month == 12:
            last_day = datetime.date(year + 1, 1, 1) - datetime.timedelta(days=1)
        else:
            last_day = datetime.date(year, month + 1, 1) - datetime.timedelta(days=1)
        
        # Calculate weekly summaries for the month
        weekly_summaries = []
        current_date = first_day
        
        while current_date <= last_day:
            # Find Monday of this week
            week_start = current_date - datetime.timedelta(days=current_date.weekday())
            week_summary = self.get_week_summary(week_start)
            weekly_summaries.append(week_summary)
            
            # Move to next week
            current_date = week_start + datetime.timedelta(days=7)
        
        # Calculate month totals
        total_hours = datetime.timedelta(0)
        total_sessions = 0
        working_days = 0
        
        for week in weekly_summaries:
            total_hours += week['total_hours']
            total_sessions += week['sessions_count']
            working_days += week['working_days']
        
        return {
            'year': year,
            'month': month,
            'month_name': datetime.date(year, month, 1).strftime('%B'),
            'first_day': first_day,
            'last_day': last_day,
            'weekly_summaries': weekly_summaries,
            'total_hours': total_hours,
            'total_sessions': total_sessions,
            'working_days': working_days,
            'average_daily_hours': datetime.timedelta(seconds=total_hours.total_seconds() / working_days) if working_days > 0 else datetime.timedelta(0)
        }
    
    def _format_duration(self, duration: datetime.timedelta) -> str:
        """Format a duration as HH:MM:SS."""
        total_seconds = int(duration.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        seconds = total_seconds % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

# Test function
def test_reporting():
    """Test the reporting functionality."""
    timer = WorkTimer()
    reporter = WeeklyReporter(timer)
    
    print("Weekly Report:")
    print(reporter.generate_text_report())
    print("\nTimesheet Summary:")
    print(reporter.generate_timesheet_summary())

if __name__ == "__main__":
    test_reporting()
