#!/usr/bin/env python3
"""
Work Timer Background Service

This version runs without a console window and is designed to run persistently
in the background. The .pyw extension ensures no command window appears.

This file should be used for background operation while work_timer.py can still
be used for debugging with console output.
"""

import sys
import os
import logging
import traceback
from pathlib import Path

# Set up logging to file since we won't have console output
log_file = Path(__file__).parent / "work_timer.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()  # Still log to console if available
    ]
)

logger = logging.getLogger(__name__)

def main():
    """Main entry point for the background service."""
    try:
        logger.info("Starting Work Timer Background Service")
        
        # Import our main application
        from work_timer import WorkTimerApp
        
        # Create and run the application in tray-only mode
        app = WorkTimerApp()
        
        # Run in background mode (tray only, no GUI by default)
        logger.info("Work Timer service started successfully")
        app.run(show_gui=False, use_tray=True)
        
    except ImportError as e:
        logger.error(f"Failed to import required modules: {e}")
        logger.error("Make sure all dependencies are installed: pip install -r requirements.txt")
    except Exception as e:
        logger.error(f"Unexpected error in Work Timer service: {e}")
        logger.error(traceback.format_exc())
    finally:
        logger.info("Work Timer Background Service stopped")

if __name__ == "__main__":
    main()
