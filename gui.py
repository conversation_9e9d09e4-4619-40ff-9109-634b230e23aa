import tkinter as tk
from tkinter import ttk, messagebox
import threading
import datetime
from timer_core import WorkTimer
from typing import Optional

class TimerGUI:
    """Simple GUI for the work timer application."""
    
    def __init__(self, timer: WorkTimer):
        self.timer = timer
        self.root = tk.Tk()
        self.root.title("Work Timer")
        self.root.geometry("400x300")
        self.root.resizable(False, False)
        
        # Variables for dynamic updates
        self.status_var = tk.StringVar()
        self.current_time_var = tk.StringVar()
        self.today_total_var = tk.StringVar()
        self.week_total_var = tk.StringVar()
        
        self._setup_ui()
        self._update_display()
        self._start_update_timer()
    
    def _setup_ui(self):
        """Set up the user interface."""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="Work Timer", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Status section
        status_frame = ttk.LabelFrame(main_frame, text="Current Status", padding="10")
        status_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(status_frame, text="Timer Status:").grid(row=0, column=0, sticky=tk.W)
        ttk.Label(status_frame, textvariable=self.status_var, font=("Arial", 10, "bold")).grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(status_frame, text="Current Session:").grid(row=1, column=0, sticky=tk.W)
        ttk.Label(status_frame, textvariable=self.current_time_var, font=("Arial", 10, "bold")).grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        # Controls section
        controls_frame = ttk.LabelFrame(main_frame, text="Controls", padding="10")
        controls_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.toggle_button = ttk.Button(controls_frame, text="Start Timer", command=self._toggle_timer)
        self.toggle_button.grid(row=0, column=0, padx=(0, 10))
        
        ttk.Button(controls_frame, text="View Weekly Report", command=self._show_weekly_report).grid(row=0, column=1)
        
        # Statistics section
        stats_frame = ttk.LabelFrame(main_frame, text="Statistics", padding="10")
        stats_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(stats_frame, text="Today's Total:").grid(row=0, column=0, sticky=tk.W)
        ttk.Label(stats_frame, textvariable=self.today_total_var, font=("Arial", 10, "bold")).grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(stats_frame, text="This Week:").grid(row=1, column=0, sticky=tk.W)
        ttk.Label(stats_frame, textvariable=self.week_total_var, font=("Arial", 10, "bold")).grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        # Hotkey info
        info_frame = ttk.Frame(main_frame)
        info_frame.grid(row=4, column=0, columnspan=2, pady=(10, 0))
        
        ttk.Label(info_frame, text="Global Hotkey: Ctrl+Shift+T", font=("Arial", 9), foreground="gray").grid(row=0, column=0)
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
    
    def _toggle_timer(self):
        """Toggle the timer state."""
        try:
            is_running, completed_session = self.timer.toggle_timer()
            
            if is_running:
                self._show_notification("Timer Started", "Work timer has been started.")
            else:
                if completed_session:
                    duration_str = self._format_duration(completed_session.duration)
                    self._show_notification("Timer Stopped", f"Work session completed: {duration_str}")
            
            self._update_display()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to toggle timer: {str(e)}")
    
    def _update_display(self):
        """Update all display elements with current data."""
        # Update status
        if self.timer.is_running():
            self.status_var.set("RUNNING")
            self.toggle_button.config(text="Stop Timer")
        else:
            self.status_var.set("STOPPED")
            self.toggle_button.config(text="Start Timer")
        
        # Update current session time
        current_duration = self.timer.get_current_session_duration()
        self.current_time_var.set(self._format_duration(current_duration))
        
        # Update today's total
        today_total = self.timer.get_today_total()
        self.today_total_var.set(self._format_duration(today_total))
        
        # Update week total
        week_total = self.timer.get_week_total()
        self.week_total_var.set(self._format_duration(week_total))
    
    def _format_duration(self, duration: datetime.timedelta) -> str:
        """Format a duration as HH:MM:SS."""
        total_seconds = int(duration.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        seconds = total_seconds % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    
    def _start_update_timer(self):
        """Start a timer to update the display every second."""
        def update_loop():
            self._update_display()
            self.root.after(1000, update_loop)
        
        update_loop()
    
    def _show_weekly_report(self):
        """Show a detailed weekly report in a new window."""
        report_window = tk.Toplevel(self.root)
        report_window.title("Weekly Report")
        report_window.geometry("500x400")
        report_window.resizable(True, True)
        
        # Create scrollable text widget
        text_frame = ttk.Frame(report_window)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Courier", 10))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Generate report content
        report_content = self._generate_weekly_report()
        text_widget.insert(tk.END, report_content)
        text_widget.config(state=tk.DISABLED)
        
        # Close button
        ttk.Button(report_window, text="Close", command=report_window.destroy).pack(pady=10)
    
    def _generate_weekly_report(self) -> str:
        """Generate a detailed weekly report."""
        today = datetime.date.today()
        week_start = today - datetime.timedelta(days=today.weekday())
        
        report = f"Weekly Report\n"
        report += f"Week of {week_start.strftime('%B %d, %Y')}\n"
        report += "=" * 50 + "\n\n"
        
        total_week_time = datetime.timedelta(0)
        
        for i in range(7):
            current_date = week_start + datetime.timedelta(days=i)
            day_total = self.timer.get_date_total(current_date)
            total_week_time += day_total
            
            day_name = current_date.strftime('%A')
            date_str = current_date.strftime('%m/%d')
            time_str = self._format_duration(day_total)
            
            if current_date == today:
                report += f"{day_name:10} {date_str}  {time_str} (Today)\n"
            else:
                report += f"{day_name:10} {date_str}  {time_str}\n"
        
        report += "-" * 30 + "\n"
        report += f"{'Total:':10}       {self._format_duration(total_week_time)}\n\n"
        
        # Add recent sessions
        report += "Recent Sessions:\n"
        report += "-" * 20 + "\n"
        
        recent_sessions = self.timer.get_recent_sessions(7)
        if recent_sessions:
            for session in recent_sessions[:10]:  # Show last 10 sessions
                start_str = session.start_time.strftime('%m/%d %H:%M')
                duration_str = self._format_duration(session.duration)
                report += f"{start_str}  {duration_str}\n"
        else:
            report += "No recent sessions found.\n"
        
        return report
    
    def _show_notification(self, title: str, message: str):
        """Show a simple notification (can be enhanced with system notifications later)."""
        # For now, just print to console - will be enhanced with system notifications
        print(f"NOTIFICATION: {title} - {message}")
    
    def run(self):
        """Start the GUI main loop."""
        self.root.mainloop()
    
    def destroy(self):
        """Clean up and destroy the GUI."""
        self.root.destroy()

# Test function for the GUI
def test_gui():
    """Test function to run the GUI standalone."""
    timer = WorkTimer()
    gui = TimerGUI(timer)
    
    try:
        gui.run()
    except KeyboardInterrupt:
        print("Shutting down...")
    finally:
        gui.destroy()

if __name__ == "__main__":
    test_gui()
