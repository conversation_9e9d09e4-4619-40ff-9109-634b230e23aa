# Work Timer

A comprehensive work timer application with hotkey support, system tray integration, and weekly reporting for timesheet management.

## Features

- **Global Hotkey Control**: Start/stop timer with Ctrl+Shift+T (customizable)
- **Background Operation**: Runs safely without command window, persists after closing
- **Manual Time Entry**: Add missed work hours with date/time selection
- **System Tray Integration**: Runs in background with easy access
- **Automatic Time Logging**: All sessions saved to SQLite database
- **Weekly Reports**: Detailed reports with timesheet-ready summaries
- **Smart Notifications**: System notifications for timer events and weekly summaries
- **Flexible GUI**: Optional GUI interface for manual control
- **Persistent Data**: Resume active sessions after application restart
- **Windows Startup**: Automatically start with Windows
- **Configurable Settings**: Customize hotkeys, notifications, and work week preferences

## Quick Start (Windows)

1. **Easy Installation**: Double-click `install_and_run.bat`
   - This will install dependencies and start the application
   - Requires Python 3.7+ (download from https://python.org if needed)

2. **Background Mode** (Recommended):
   - Double-click `start_background.bat` - Runs without command window
   - Or double-click `work_timer_service.pyw` directly
   - Add to startup: Run `add_to_startup.bat` as administrator

3. **Manual Installation**:
   ```bash
   pip install -r requirements.txt
   python work_timer.py
   ```

## Usage

### Basic Operation
- **Start/Stop Timer**: Press `Ctrl+Shift+T` anywhere on your system
- **System Tray**: Look for the timer icon in your system tray
- **Right-click tray icon** for options:
  - Toggle Timer
  - Show Status
  - Add Manual Time ⭐ NEW
  - Weekly Report
  - Timesheet Summary
  - Quit

### Manual Time Entry
- **Add Missed Hours**: Right-click tray → "Add Manual Time"
- **Two Entry Methods**:
  - Enter duration (e.g., "2.5 hours")
  - Enter start/end times (e.g., "9:00 AM to 5:30 PM")
- **Date Selection**: Add time for any past date
- **Descriptions**: Optional notes for each entry

### Command Line Options
```bash
python work_timer.py [options]

Options:
  --gui           Show GUI window on startup
  --no-tray       Don't start system tray (GUI only mode)
  --config FILE   Use custom config file
  --help          Show help message
```

### Running Modes
1. **Background Service** (recommended): `start_background.bat` or `work_timer_service.pyw`
2. **System Tray Mode**: `python work_timer.py` (with console)
3. **GUI Mode**: `python work_timer.py --gui`
4. **Both**: GUI + Tray for maximum flexibility
5. **Headless**: `python work_timer.py --no-tray` (hotkeys only)

### Management Scripts
- **Start Background**: `start_background.bat` - Start without console window
- **Stop Timer**: `stop_timer.bat` - Stop background service
- **Check Status**: `check_timer_status.bat` - See if timer is running
- **Add to Startup**: `add_to_startup.bat` - Auto-start with Windows

## Configuration

Edit `config.ini` to customize:

### Hotkeys
```ini
[HOTKEYS]
toggle_timer = ctrl+shift+t
show_status = ctrl+shift+s
```

### Notifications
```ini
[NOTIFICATIONS]
notify_on_start = true
notify_on_stop = true
notify_weekly_summary = true
weekly_notification_day = 4    # Friday
weekly_notification_hour = 17  # 5 PM
```

### Work Week Settings
```ini
[WORK_WEEK]
start_day = 0                  # Monday
work_days = 5                  # Monday-Friday
standard_hours_per_day = 8.0
standard_hours_per_week = 40.0
```

## Weekly Reports

The application automatically generates weekly reports perfect for timesheet entry:

- **Automatic Friday Notifications**: Get weekly summaries at 5 PM on Fridays
- **Detailed Breakdown**: Daily hours, total hours, working days
- **Timesheet Format**: Ready-to-copy summary for your timesheet system
- **Session History**: View individual work sessions

Access reports via:
- System tray right-click menu
- GUI "View Weekly Report" button
- Automatic Friday notifications

## Data Storage

- **Database**: `work_sessions.db` (SQLite)
- **Sessions**: Start time, end time, duration
- **Persistence**: Active sessions resume after restart
- **Backup**: Configurable automatic backups

## Testing

Run the test suite to verify everything works:
```bash
python test_timer.py
```

## Troubleshooting

### Common Issues

1. **Hotkeys not working**:
   - Run as administrator (Windows)
   - Check if another application is using the same hotkey
   - Try a different hotkey combination in `config.ini`

2. **System tray icon not appearing**:
   - Check Windows system tray settings
   - Try running with `--gui` flag instead

3. **Notifications not showing**:
   - Check Windows notification settings
   - Verify `plyer` package is installed correctly

4. **Permission errors**:
   - Run command prompt as administrator
   - Check file permissions in the application directory

### Dependencies
- Python 3.7+
- pystray (system tray)
- keyboard (global hotkeys)
- plyer (notifications)
- Pillow (icon generation)
- tkinter (GUI - usually included with Python)

## File Structure

```
Work Timer/
├── work_timer.py              # Main application (with console)
├── work_timer_service.pyw     # Background service (no console) ⭐
├── timer_core.py              # Core timer functionality
├── hotkey_manager.py          # Global hotkey handling
├── notifications.py           # System notifications
├── system_tray.py             # System tray integration
├── gui.py                     # GUI interface
├── manual_entry.py            # Manual time entry ⭐
├── reporting.py               # Weekly reports
├── config.py                  # Configuration management
├── test_timer.py              # Test suite
├── config.ini                 # Configuration file
├── requirements.txt           # Python dependencies
├── install_and_run.bat        # Windows installer
├── start_background.bat       # Start without console ⭐
├── stop_timer.bat             # Stop background service ⭐
├── check_timer_status.bat     # Check if running ⭐
├── add_to_startup.bat         # Add to Windows startup ⭐
└── README.md                  # This file
```
