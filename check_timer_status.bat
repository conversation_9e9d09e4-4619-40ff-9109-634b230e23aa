@echo off
echo Checking Work Timer Status...
echo =============================

REM Check if pythonw process is running with work_timer_service.pyw
tasklist /fi "imagename eq pythonw.exe" | find "pythonw.exe" >nul

if %errorlevel% == 0 (
    echo ✓ Work Timer appears to be running
    echo.
    echo Active Python processes:
    tasklist /fi "imagename eq pythonw.exe" /fo table
    echo.
    echo Check your system tray for the timer icon.
) else (
    echo ✗ Work Timer is not running
    echo.
    echo To start the timer, run: start_background.bat
    echo Or double-click: work_timer_service.pyw
)

echo.
echo Log file location: work_timer.log
if exist work_timer.log (
    echo Last few log entries:
    echo --------------------
    powershell "Get-Content work_timer.log -Tail 5"
) else (
    echo No log file found.
)

echo.
pause
